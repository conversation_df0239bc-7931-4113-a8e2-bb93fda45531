/**
 * 下载成功弹窗组件
 */

'use client'

import { useCallback } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { useTranslation } from '@/lib/i18n/client'
import Image from 'next/image'
import retentionSuccess from '/public/images/retentionSuccess.png'

interface DownloadSuccessModalProps {
  isOpen: boolean
  onClose: () => void
  onDownload: () => void
  isDownloading?: boolean
}

export default function DownloadSuccessModal({
  isOpen,
  onClose,
  onDownload,
  isDownloading = false
}: DownloadSuccessModalProps) {
  const { t } = useTranslation()

  const handleDownload = useCallback(() => {
    onDownload()
  }, [onDownload])

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center mt-[89px] md:mt-[109px] bg-[#ccc]"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className="relative  bg-white w-[90%] margin-auto md:w-[440px] pb-10"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 内容区域 */}
            <div className="text-center">
              {/* 成功图标 */}
              <div className="pt-[50px] pb-4">
                <div className="w-15 h-15 mx-auto">
                  <Image
                    src={retentionSuccess}
                    alt="Success"
                    width={60}
                    height={60}
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>

              {/* 标题和描述 */}
              <div className="mb-10">
                <h2 className="text-[20px] font-[500] leading-[28px] mb-2 text-black">
                  {t('retention.success.title', '已提交')}
                </h2>
                <p className="text-sm font-[300] leading-[18px] mb-[50px] text-[#444] px-2">
                  {t('retention.success.message', '您已填写信息，免费下载图片')}
                </p>
              </div>

              {/* 下载按钮 */}
              <button
                onClick={handleDownload}
                disabled={isDownloading}
                className="w-[60%] md:w-[50%]  mx-auto py-4 bg-black  text-white text-[18px] leading-[25px]"
              >
                {isDownloading ? t('retention.success.downloading', '下载中...') : t('retention.success.downloadButton', '下载图片')}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
